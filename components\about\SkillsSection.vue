<template>
  <div class="snap-section flex flex-col items-center justify-center px-4 md:px-6 lg:px-8 xl:px-12 my-auto py-12 md:py-8 overscroll-none">
    <div class="w-full md:max-w-2xl lg:max-w-4xl mx-auto flex flex-col items-center justify-center">
      <div class="text-center flex flex-col items-center w-full gap-3 md:gap-4 h-full">
        <GlassContainer id="skillsHeader" class="inline-block z-30 w-auto" padding="py-4 px-4 md:px-6" rounded="rounded-xl">
          <h2 ref="skillsTitle" class="text-xl md:text-3xl font-bold mb-4 md:mb-6 z-30" :style="{ color: themeStore.themeColors.primary }">
            My Skills
          </h2>

          <!-- Combined Toggle Switch for Skills and CV -->
          <div class="flex flex-col items-center gap-2 justify-center">
            <ToggleSwitch
              v-model="isGameDev"
              left-label="Web Dev"
              right-label="Game Dev"
              @update:modelValue="handleToggle"
            />

            <!-- CV Button -->
            <a ref="cvButton" @click.prevent="openCVInNewTab"
               id="view-cv-button"
               class="group relative inline-flex items-center justify-center px-4 py-1.5 md:px-6 md:py-2 overflow-hidden rounded-md text-white font-medium text-xs md:text-sm mt-1 cursor-pointer"
               :class="buttonClass">
              <span class="relative z-10 flex items-center justify-center space-x-1.5">
                <span>View {{ isGameDev ? 'Game Dev' : 'Web Dev' }} CV</span>
                <Icon icon="mdi:arrow-right" class="w-3.5 h-3.5 md:w-4 md:h-4 transform transition-transform duration-500 group-hover:translate-x-1" />
              </span>
              <span class="absolute inset-0 z-0" :class="buttonGradientClass"></span>
              <span class="absolute bottom-0 left-0 h-1 transition-all duration-500 ease-out origin-left group-hover:w-full w-0" :class="buttonHighlightClass"></span>
            </a>
          </div>
        </GlassContainer>

        <!-- Skills Grid Container - Fully responsive with auto sizing -->
        <div class="relative z-10 w-full mx-auto mt-2 md:mt-3 flex-grow overflow-hidden touch-pan-y">
          <!-- Web Skills Grid - Adjusted for better responsiveness -->
          <div
            class="grid grid-cols-3 sm:grid-cols-4 lg:grid-cols-6 gap-2 sm:gap-3 md:gap-3 w-full p-2 md:p-3 skills-grid no-scrollbar"
            ref="webSkillsGrid"
            :style="{ display: isGameDev ? 'none' : 'grid' }"
          >
            <SkillItem
              v-for="(skill, index) in webSkills"
              :key="`web-${index}`"
              :name="skill.name"
              :icon="skill.icon"
              class="web-skill"
            />
          </div>

          <!-- Game Skills Grid - Adjusted for better responsiveness -->
          <div
            class="grid grid-cols-3 sm:grid-cols-4 lg:grid-cols-6 gap-2 sm:gap-3 md:gap-3 w-full p-2 md:p-3 skills-grid no-scrollbar"
            ref="gameSkillsGrid"
            :style="{ display: isGameDev ? 'grid' : 'none' }"
          >
            <SkillItem
              v-for="(skill, index) in gameSkills"
              :key="`game-${index}`"
              :name="skill.name"
              :icon="skill.icon"
              class="game-skill"
            />
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, onMounted, onUnmounted, computed, nextTick, watch } from 'vue';
import { useNuxtApp } from '#app';
import ToggleSwitch from '../ui/ToggleSwitch.vue';
import SkillItem from '../ui/SkillItem.vue';
import GlassContainer from '../ui/GlassContainer.vue';
import { Icon } from '@iconify/vue';
import { useThemeStore } from '~/stores/theme-optimized';

// Refs for animation targets
const skillsTitle = ref(null);
const webSkillsGrid = ref(null);
const gameSkillsGrid = ref(null);
const cvButton = ref(null);

// Use the theme store
const themeStore = useThemeStore();

// Computed property for isGameDev to sync with the store
const isGameDev = computed({
  get: () => themeStore.isGameDev,
  set: (value) => {
    if (value) {
      themeStore.setGameDev();
    } else {
      themeStore.setWebDev();
    }
  }
});

// Computed properties for button styling
const buttonClass = computed(() => {
  return themeStore.isGameDev ? 'bg-primary-button-green' : 'bg-primary-button-blue';
});

const buttonGradientClass = computed(() => {
  return themeStore.isGameDev
    ? 'bg-gradient-to-r from-green-600 to-green-500'
    : 'bg-gradient-to-r from-blue-600 to-blue-500';
});

const buttonHighlightClass = computed(() => {
  return themeStore.isGameDev ? 'bg-green-400' : 'bg-blue-400';
});

// Skill data
const webSkills = [
  { name: 'HTML5', icon: 'vscode-icons:file-type-html' },
  { name: 'CSS3', icon: 'vscode-icons:file-type-css' },
  { name: 'JavaScript', icon: 'logos:javascript' },
  { name: 'TypeScript', icon: 'logos:typescript-icon' },
  { name: 'Vue.js', icon: 'logos:vue' },
  { name: 'Nuxt', icon: 'logos:nuxt-icon' },
  { name: 'Vite', icon: 'logos:vitejs' },
  { name: 'Ionic', icon: 'logos:ionic-icon' },
  { name: 'G Cloud', icon: 'logos:google-cloud' },
  { name: 'Firebase', icon: 'logos:firebase' },
  { name: 'Git', icon: 'logos:git-icon' },
  { name: 'GitHub', icon: 'mdi:github' }
];

const gameSkills = [
  { name: 'C#', icon: 'devicon:csharp' },
  { name: 'C++', icon: 'devicon:cplusplus' },
  { name: 'Python', icon: 'logos:python' },
  { name: 'JavaScript', icon: 'logos:javascript' },
  { name: 'GameMaker', icon: 'simple-icons:gamemaker' },
  { name: 'Unity', icon: 'bi:unity' },
  { name: 'Unreal', icon: 'simple-icons:unrealengine' },
  { name: 'PlayFab', icon: 'logos:microsoft-icon' },
  { name: 'Firebase', icon: 'logos:firebase' },
  { name: 'GitHub', icon: 'mdi:github' },
  { name: 'Git', icon: 'logos:git-icon' }
];

// Handle combined toggle for both skills and CV
const handleToggle = (value) => {
  // Get the nuxt app to access our animation utilities
  const nuxtApp = useNuxtApp();
  const { $gsap: gsap } = nuxtApp;

  // Make sure GSAP is available
  if (!gsap) {
    console.warn('GSAP not available, animations skipped');
    // Still update the theme store even if animations fail
    isGameDev.value = value;
    return;
  }

  // Update the theme store first - this will trigger the reactive display changes
  isGameDev.value = value;

  // Get the grid that will become visible
  const fadeInGrid = value ? gameSkillsGrid.value : webSkillsGrid.value;

  // Make sure the grid exists
  if (!fadeInGrid) return;

  // Get all skill items in the new grid
  const fadeInItems = fadeInGrid.querySelectorAll('.skill-item');

  // Create entrance animation for the new grid
  if (fadeInItems.length > 0) {
    // Set initial state for animation
    gsap.set(fadeInItems, {
      opacity: 0,
      scale: 0.9,
      y: 20
    });

    // Animate items in
    gsap.to(fadeInItems, {
      opacity: 1,
      scale: 1,
      y: 0,
      stagger: {
        each: 0.03,
        from: "start",
        grid: "auto"
      },
      duration: 0.6,
      ease: "power2.out",
      onComplete: () => {
        // Clean up transforms to prevent text fuzziness
        fadeInItems.forEach(item => {
          item.style.transform = 'translateZ(0)';
        });
      }
    });
  }

  // Animate the CV button if it exists
  if (cvButton.value) {
    // Create a fancy animation for the CV button
    const buttonTl = gsap.timeline();

    // First, create a quick pulse effect
    buttonTl.to(cvButton.value, {
      scale: 0.9,
      duration: 0.15,
      ease: 'power2.in'
    })
    .to(cvButton.value, {
      scale: 1.05,
      duration: 0.2,
      ease: 'back.out(3)'
    })
    .to(cvButton.value, {
      scale: 1,
      duration: 0.15,
      ease: 'power1.out'
    });

    // Add a color flash effect to the button
    const buttonHighlight = cvButton.value.querySelector('.absolute.inset-0');
    if (buttonHighlight) {
      buttonTl.fromTo(buttonHighlight,
        { opacity: 0.7 },
        { opacity: 1, duration: 0.2, ease: 'power1.inOut' },
        "-=0.5"
      )
      .to(buttonHighlight,
        { opacity: 0.9, duration: 0.3, ease: 'power1.out' },
        "-=0.3"
      );
    }

    // Add a glow effect to the text
    const buttonText = cvButton.value.querySelector('.relative.z-10');
    if (buttonText) {
      buttonTl.fromTo(buttonText,
        { textShadow: '0 0 0 rgba(255,255,255,0)' },
        {
          textShadow: '0 0 10px rgba(255,255,255,0.8), 0 0 20px rgba(255,255,255,0.4)',
          duration: 0.3,
          ease: 'power2.in'
        },
        "-=0.4"
      )
      .to(buttonText, {
        textShadow: '0 0 0 rgba(255,255,255,0)',
        duration: 0.5,
        ease: 'power2.out'
      });
    }
  }
};

// Function to open CV in a new tab using direct static URL
const openCVInNewTab = () => {
  // Determine which CV to open based on the toggle state
  const cvPath = isGameDev.value ? '/cv-game-dev.pdf' : '/cv-web-dev.pdf';

  // Open the PDF directly in a new tab using the static URL
  window.open(cvPath, '_blank');
};

// Create a ref for the intersection observer
const observer = ref(null);

// Setup for intersection observer animation
const setupIntersectionObserver = () => {
  // Check if we're in a browser environment
  if (typeof window === 'undefined' || !document) return;

  // Get the nuxt app to access our animation utilities
  const nuxtApp = useNuxtApp();
  const { $gsap: gsap } = nuxtApp;

  // Make sure GSAP is available
  if (!gsap) {
    console.warn('GSAP not available, intersection observer animation skipped');
    return;
  }

  // Use nextTick to ensure the DOM is fully rendered
  nextTick(() => {
    // Create a new intersection observer
    observer.value = new IntersectionObserver((entries) => {
      entries.forEach(entry => {
        if (entry.isIntersecting) {
          // Animate skills section entrance
          gsap.from(skillsTitle.value, {
            opacity: 0,
            y: 50,
            duration: 0.6,
            delay: 0.2
          });

          // Determine which grid is visible based on current theme
          const visibleGrid = themeStore.isGameDev ? gameSkillsGrid.value : webSkillsGrid.value;

          // Animate only the visible skill items
          if (visibleGrid) {
            const skillItems = visibleGrid.querySelectorAll('.skill-item');

            // Use a timeline for better control
            const tl = gsap.timeline({ delay: 0.4 });

            // Simple animation without 3D transforms
            tl.from(skillItems, {
              opacity: 0,
              y: 20,
              duration: 0.6,
              stagger: {
                each: 0.05,
                from: "start",
                grid: "auto"
              }
            })

            // Ensure transforms are reset to prevent text fuzziness
            .set(skillItems, {
              clearProps: "transform",
              onComplete: () => {
                // Force browser to re-render text with proper anti-aliasing
                skillItems.forEach(item => {
                  item.style.transform = 'translateZ(0)';
                });
              }
            });
          }

          // Unobserve after animation
          observer.value.unobserve(entry.target);
        }
      });
    }, { threshold: 0.2 });

    // Observe the skills section
    const skillsSection = document.querySelector('.snap-section:nth-child(2)');
    if (skillsSection) {
      observer.value.observe(skillsSection);
    }
  });
};

// Initialize button animations
const initButtonAnimation = () => {
  // Get the nuxt app to access our animation utilities
  const nuxtApp = useNuxtApp();
  const { $gsap: gsap } = nuxtApp;

  // Make sure GSAP is available
  if (!gsap) {
    console.warn('GSAP not available, button animation skipped');
    return;
  }

  // Check if we're in a browser environment
  if (typeof window === 'undefined' || !document) return;

  const button = document.getElementById('view-cv-button');
  if (!button) return;

  // Function to update button shadow color based on theme
  const updateButtonShadow = () => {
    const shadowColor = themeStore.isGameDev
      ? 'rgba(0, 50, 0, 0.3)' // Green shadow
      : 'rgba(0, 30, 60, 0.3)'; // Blue shadow

    const baseShadowColor = themeStore.isGameDev
      ? 'rgba(0, 50, 0, 0.2)' // Green base shadow
      : 'rgba(0, 30, 60, 0.2)'; // Blue base shadow

    // Update button shadow
    gsap.to(button, {
      boxShadow: `0 4px 6px ${baseShadowColor}`,
      duration: 0.3
    });

    // Store shadow colors for hover effect
    button.dataset.hoverShadow = `0 6px 16px ${shadowColor}`;
    button.dataset.baseShadow = `0 4px 6px ${baseShadowColor}`;
  };

  // Initial shadow setup
  updateButtonShadow();

  // Watch for theme changes to update button shadow
  watch(() => themeStore.isGameDev, () => {
    updateButtonShadow();
  });

  // Create hover effect handlers with GSAP
  const handleMouseEnter = () => {
    gsap.to(button, {
      y: -4,
      boxShadow: button.dataset.hoverShadow,
      duration: 0.3,
      ease: 'power2.out'
    });
  };

  const handleMouseLeave = () => {
    gsap.to(button, {
      y: 0,
      boxShadow: button.dataset.baseShadow,
      duration: 0.3,
      ease: 'power2.out'
    });
  };

  const handleMouseDown = () => {
    gsap.to(button, {
      scale: 0.97,
      duration: 0.1,
      ease: 'power2.in'
    });
  };

  const handleMouseUp = () => {
    gsap.to(button, {
      scale: 1,
      duration: 0.2,
      ease: 'power2.out'
    });
  };

  // Add event listeners
  button.addEventListener('mouseenter', handleMouseEnter);
  button.addEventListener('mouseleave', handleMouseLeave);
  button.addEventListener('mousedown', handleMouseDown);
  button.addEventListener('mouseup', handleMouseUp);

  // Store the handlers on the button for cleanup
  button._gsapHandlers = {
    mouseenter: handleMouseEnter,
    mouseleave: handleMouseLeave,
    mousedown: handleMouseDown,
    mouseup: handleMouseUp
  };
};

// Clean up function
const cleanupAnimation = () => {
  const button = document.getElementById('view-cv-button');
  if (!button) return;

  // Remove event listeners using the stored handlers
  if (button._gsapHandlers) {
    button.removeEventListener('mouseenter', button._gsapHandlers.mouseenter);
    button.removeEventListener('mouseleave', button._gsapHandlers.mouseleave);
    button.removeEventListener('mousedown', button._gsapHandlers.mousedown);
    button.removeEventListener('mouseup', button._gsapHandlers.mouseup);

    // Clean up the reference
    delete button._gsapHandlers;
  }
};

// Prevent scroll propagation
const preventScrollPropagation = (event) => {
  // For wheel events
  if (event.type === 'wheel') {
    // Check if the element is scrolled to the bottom
    const isAtBottom = event.target.scrollHeight - event.target.scrollTop <= event.target.clientHeight + 1;
    // Check if the element is scrolled to the top
    const isAtTop = event.target.scrollTop <= 0;
    // Check if all content fits without scrolling
    const noScroll = event.target.scrollHeight <= event.target.clientHeight;

    // If all content fits without scrolling, allow propagation
    if (noScroll) {
      return;
    }

    // If we're at the top and trying to scroll up, or at the bottom and trying to scroll down, allow propagation
    if ((isAtTop && event.deltaY < 0) || (isAtBottom && event.deltaY > 0)) {
      return;
    }

    // Otherwise, prevent the event from propagating to parent
    event.stopPropagation();

    // Also prevent default behavior for upward scrolls at the top
    // This is crucial to prevent section scrolling when at the top
    if (isAtTop && event.deltaY < 0) {
      event.preventDefault();
    }
  }
};

onMounted(() => {
  // Get the nuxt app to access our animation utilities
  const nuxtApp = useNuxtApp();
  const { $gsap: gsap } = nuxtApp;

  // Make sure GSAP is available
  if (!gsap) {
    console.warn('GSAP not available, animations skipped');
    return;
  }

  // Initialize with a short delay to ensure DOM is fully ready
  // This is crucial for handling page refreshes correctly
  setTimeout(() => {
    setupIntersectionObserver();
    initButtonAnimation();

    // Initialize skills grid based on current theme
    if (webSkillsGrid.value && gameSkillsGrid.value) {
      try {
        // Add wheel event listeners to prevent scroll propagation
        // Using { passive: false } is crucial to allow preventDefault() to work
        webSkillsGrid.value.addEventListener('wheel', preventScrollPropagation, { passive: false });
        gameSkillsGrid.value.addEventListener('wheel', preventScrollPropagation, { passive: false });

        // Also add touch events for mobile
        webSkillsGrid.value.addEventListener('touchstart', (e) => e.stopPropagation(), { passive: false });
        gameSkillsGrid.value.addEventListener('touchstart', (e) => e.stopPropagation(), { passive: false });

        // Force layout recalculation
        webSkillsGrid.value.offsetHeight;
        gameSkillsGrid.value.offsetHeight;

        // Get the currently active grid based on theme
        const activeGrid = themeStore.isGameDev ? gameSkillsGrid.value : webSkillsGrid.value;

        // Create a fancy initial animation for the active grid's skill items
        const skillItems = activeGrid.querySelectorAll('.skill-item');

        // Set initial state for all items (removed 3D transforms)
        gsap.set(skillItems, {
          opacity: 0,
          scale: 0.9,
          y: 20
        });

        // Create a timeline for the initial animation
        const initialTl = gsap.timeline({ delay: 0.3 });

        // Animate items with a simpler entrance animation
        initialTl.to(skillItems, {
          opacity: 1,
          scale: 1,
          y: 0,
          duration: 0.6,
          stagger: {
            each: 0.03,
            from: "start",
            grid: "auto"
          },
          ease: "power2.out"
        })

        // Add a subtle highlight effect
        .to(skillItems, {
          boxShadow: "0 0 10px rgba(var(--primary-rgb), 0.3)",
          duration: 0.4,
          stagger: {
            each: 0.02,
            from: "start",
            grid: "auto"
          },
          ease: "power2.inOut"
        }, "-=0.3")

        // Return to normal state
        .to(skillItems, {
          boxShadow: "0 0 0 rgba(var(--primary-rgb), 0)",
          duration: 0.4,
          stagger: {
            each: 0.01,
            from: "start",
            grid: "auto"
          },
          ease: "power2.out"
        }, "-=0.1")

        // Ensure transforms are reset to prevent text fuzziness
        .set(skillItems, {
          clearProps: "transform",
          onComplete: () => {
            // Force browser to re-render text with proper anti-aliasing
            skillItems.forEach(item => {
              item.style.transform = 'translateZ(0)';
            });
          }
        });
      } catch (error) {
        console.error('Error initializing skills grid:', error);
      }
    }
  }, 50);

  // Use the ensureTheme helper from our plugin to ensure theme is correct
  if (nuxtApp.$ensureTheme) {
    nextTick(() => {
      nuxtApp.$ensureTheme();
    });
  }
});

// Clean up event listeners when component is unmounted
onUnmounted(() => {
  // Clean up event listeners
  cleanupAnimation();

  // Remove all event listeners
  if (webSkillsGrid.value) {
    webSkillsGrid.value.removeEventListener('wheel', preventScrollPropagation);
    webSkillsGrid.value.removeEventListener('touchstart', (e) => e.stopPropagation());
  }

  if (gameSkillsGrid.value) {
    gameSkillsGrid.value.removeEventListener('wheel', preventScrollPropagation);
    gameSkillsGrid.value.removeEventListener('touchstart', (e) => e.stopPropagation());
  }

  // Clean up intersection observer
  if (observer.value) {
    observer.value.disconnect();
    observer.value = null;
  }
});
</script>

<style scoped>
.bg-primary-button-green {
  background-color: var(--primary-color);
}

.bg-primary-button-blue {
  background-color: var(--primary-color);
}

#view-cv-button {
  transition: transform 0.3s, box-shadow 0.3s;
  letter-spacing: 0.5px;
}

.skills-grid {
  will-change: opacity, transform;
  transition: opacity 0.3s ease;
  max-height: 100%; /* Ensure grid doesn't exceed container height */
  overflow: visible !important; /* Prevent scrolling */
}

/* Hide scrollbar completely */
.no-scrollbar {
  -ms-overflow-style: none; /* IE and Edge */
  scrollbar-width: none; /* Firefox */
}

/* Hide scrollbar for Chrome, Safari and Opera */
.no-scrollbar::-webkit-scrollbar {
  display: none;
}

.web-skill, .game-skill {
  will-change: opacity, transform;
  transition: transform 0.3s ease, box-shadow 0.3s ease;
  /* Removed 3D transform style to prevent text fuzziness */
}

/* Ensure all skills fit on screen */
@media (max-width: 768px) {
  .skills-grid {
    padding-bottom: 0.5rem;
  }
}

/* Hover effects are now handled in the SkillItem component */
</style>
