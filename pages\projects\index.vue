<template>
  <main class="min-h-screen bg-transparent">
    <!-- SEO Component -->
    <SEO
      title="Projects"
      description="Explore my portfolio of game development and web development projects, including client work, personal projects, and open-source contributions."
      url="/projects"
    />

    <div class="container mx-auto px-4 max-w-6xl">
      <h1 class="text-3xl md:text-4xl font-bold mb-8 md:mb-12 text-center text-white">
        My Projects
      </h1>
      <div class="grid grid-cols-1 md:grid-cols-2 gap-6 md:gap-10 lg:gap-12 max-w-5xl mx-auto">
        <div
          v-for="(project, index) in projects"
          :key="project._path"
          :class="{ 'last-project': index === projects.length - 1 }"
        >
          <ProjectCard :project="project" />
        </div>
      </div>

      <!-- Scroll indicator - fixed at the bottom of the viewport -->
      <div
        class="fixed bottom-8 md:bottom-12 left-0 right-0 flex justify-center z-50"
      >
        <!-- Simple scroll indicator that doesn't rely on complex logic -->
        <div
          id="scroll-indicator"
          class="px-5 py-3 glass-effect rounded-3xl flex flex-col items-center transition-all duration-500"
        >
          <span class="text-xs md:text-sm text-white font-medium mb-1"
            >Scroll to see more projects</span
          >
          <svg
            class="w-5 h-5 md:w-6 md:h-6 animate-bounce"
            fill="none"
            stroke-linecap="round"
            stroke-linejoin="round"
            stroke-width="2.5"
            viewBox="0 0 24 24"
            :style="{ stroke: themeStore.themeColors.primary }"
          >
            <path d="M19 14l-7 7m0 0l-7-7m7 7V3"></path>
          </svg>
        </div>
      </div>

      <!-- Hidden marker at the bottom of the page to detect when we've reached the end -->
      <div id="end-marker" ref="endMarker" class="h-20 w-full"></div>
    </div>
  </main>
</template>

<script setup>
import { ref, onMounted, onUnmounted, watch } from "vue";
import { useRoute } from "vue-router";
import { useThemeStore } from "~/stores/theme-optimized";
import ProjectCard from "~/components/projects/ProjectCard.vue";

const themeStore = useThemeStore();
const route = useRoute();
const endMarker = ref(null);
let scrollObserver = null;

const { data: projects } = await useAsyncData("projects", () =>
  queryCollection("content")
    .select("title", "description", "meta", "path")
    .all()
);

// Log projects data after fetching
console.log("Projects data:", projects.value);

// Function to reset theme to default while preserving game/web preference
const resetThemeToDefault = () => {
  // Reset to default theme using the theme store
  // The resetToDefaultTheme method now preserves the game/web preference
  themeStore.resetToDefaultTheme();
};

// Function to set up the scroll indicator
const setupScrollIndicator = () => {
  // Get the scroll indicator element
  const scrollIndicator = document.getElementById("scroll-indicator");
  if (!scrollIndicator) return;

  // Initially show the indicator
  scrollIndicator.style.opacity = "1";
  scrollIndicator.style.transform = "translateY(0)";
  scrollIndicator.style.pointerEvents = "auto";

  // Keep track of whether we should hide the indicator
  let shouldHideIndicator = false;

  // Function to handle scroll events
  const handleScroll = () => {
    // Don't check if we're already at the bottom
    if (shouldHideIndicator) return;

    // Get current scroll position
    const scrollPosition = window.scrollY + window.innerHeight;
    const pageHeight = document.body.offsetHeight;

    // Calculate how close we are to the bottom (as a percentage)
    const scrollPercentage = (scrollPosition / pageHeight) * 100;

    // Check if we should hide the indicator
    shouldHideIndicator = false;

    // Check percentage - if we're near the bottom (within 10% of the page height)
    if (scrollPercentage > 90) {
      shouldHideIndicator = true;
    }

    // Check absolute distance to bottom
    const distanceToBottom = pageHeight - scrollPosition;
    if (distanceToBottom < 100) {
      shouldHideIndicator = true;
    }

    // Check if end marker is visible
    const endMarker = document.getElementById("end-marker");
    if (endMarker) {
      const rect = endMarker.getBoundingClientRect();
      // If end marker is in viewport
      if (rect.top < window.innerHeight) {
        shouldHideIndicator = true;
      }
    }

    // Check if we're at the very bottom of the page
    if (Math.ceil(scrollPosition) >= pageHeight) {
      shouldHideIndicator = true;
    }

    // Apply visibility based on our checks
    if (shouldHideIndicator) {
      scrollIndicator.style.opacity = "0";
      scrollIndicator.style.transform = "translateY(20px)";
      scrollIndicator.style.pointerEvents = "none";
    } else {
      scrollIndicator.style.opacity = "1";
      scrollIndicator.style.transform = "translateY(0)";
      scrollIndicator.style.pointerEvents = "auto";
    }
  };

  // Run once on initial load after a short delay
  setTimeout(handleScroll, 100);

  // Add scroll event listener with throttling to improve performance
  let lastScrollTime = 0;
  const scrollThrottle = 100; // ms

  const throttledScroll = () => {
    const now = Date.now();
    if (now - lastScrollTime >= scrollThrottle) {
      lastScrollTime = now;
      handleScroll();
    }
  };

  window.addEventListener("scroll", throttledScroll);

  // Also check on window resize
  window.addEventListener("resize", handleScroll);

  // Return cleanup function
  return () => {
    window.removeEventListener("scroll", throttledScroll);
    window.removeEventListener("resize", handleScroll);
  };
};

// Watch for route changes
watch(
  () => route.path,
  (newPath) => {
    if (newPath === "/projects" || newPath === "/projects/") {
      resetThemeToDefault();
    }
  },
  { immediate: true }
);

// Cleanup function for scroll event listener
let cleanupScrollListener = null;

// Cleanup when component is unmounted
onUnmounted(() => {
  // Remove scroll event listener if it exists
  if (cleanupScrollListener) {
    cleanupScrollListener();
  }

  // Clean up observer if it exists
  if (scrollObserver) {
    scrollObserver.disconnect();
    scrollObserver = null;
  }
});

// Initialize when component is mounted
onMounted(() => {
  resetThemeToDefault();

  // Force the scroll indicator to be visible for at least 5 seconds
  const scrollIndicator = document.getElementById("scroll-indicator");
  if (scrollIndicator) {
    scrollIndicator.style.opacity = "1";
    scrollIndicator.style.transform = "translateY(0)";
    scrollIndicator.style.pointerEvents = "auto";

    // Prevent any scroll detection for the first 5 seconds
    const forceVisibleTimeout = setTimeout(() => {
      // After 5 seconds, set up the scroll detection
      cleanupScrollListener = setupScrollIndicator();

      // Check again after a longer delay in case content loads later
      setTimeout(() => {
        if (cleanupScrollListener) {
          // Remove old listener
          cleanupScrollListener();
        }
        // Set up new listener with updated page dimensions
        cleanupScrollListener = setupScrollIndicator();
      }, 1500);
    }, 5000); // Keep visible for 5 seconds

    // Clean up the timeout if component is unmounted
    onUnmounted(() => {
      clearTimeout(forceVisibleTimeout);
    });
  }
});
</script>

<style scoped>
.last-project {
  position: relative;
}

.glass-effect {
  background-color: rgba(0, 0, 0, 0.7);
  backdrop-filter: blur(12px);

  border: 1px solid rgba(255, 255, 255, 0.2);
  box-shadow: 0 4px 30px rgba(0, 0, 0, 0.2);
  /* Add a subtle glow effect */
  animation: subtle-glow 3s infinite alternate;
}

@keyframes subtle-glow {
  from {
    box-shadow: 0 4px 30px rgba(0, 0, 0, 0.2);
  }
  to {
    box-shadow: 0 4px 30px rgba(0, 0, 0, 0.4),
      0 0 15px rgba(255, 255, 255, 0.15);
  }
}
</style>
